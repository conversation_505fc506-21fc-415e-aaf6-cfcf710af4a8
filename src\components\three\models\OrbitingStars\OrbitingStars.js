import * as THREE from "three";
import { EARTH_RADIUS } from "../../../../constants/index.js";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";

/**
 * 地球轨道星星系统
 *
 * 在地球表面周围创建环绕运动的星星点点效果，营造梦幻的太空氛围。
 *
 * 特性：
 * 1. 多层轨道的星星分布
 * 2. 不同速度的环绕运动
 * 3. 动态大小和透明度变化
 * 4. 可配置的轨道参数
 * 5. 与地球同步的光照效果
 *
 * @class OrbitingStars
 */
class OrbitingStars {
  constructor(scene, options = {}) {
    this.scene = scene;

    // 配置参数
    this.config = {
      starCount: options.starCount || 300, // 总星星数量 (减少)
      orbitLayers: options.orbitLayers || 5, // 轨道层数 (减少)
      minRadius: options.minRadius || EARTH_RADIUS * 1.03, // 最小轨道半径 (更贴近地球)
      maxRadius: options.maxRadius || EARTH_RADIUS * 1.05, // 最大轨道半径 (更贴近地球)
      minSize: options.minSize || 0.8, // 最小星星大小 (增大以提高亮度)
      maxSize: options.maxSize || 2.0, // 最大星星大小 (增大以提高亮度)
      minOpacity: options.minOpacity || 0.4, // 最小透明度 (提高基础亮度)
      maxOpacity: options.maxOpacity || 1.0, // 最大透明度 (保持)
      rotationSpeed: options.rotationSpeed || 0.001, // 基础旋转速度 (加快)
      twinkleSpeed: options.twinkleSpeed || 0.8, // 闪烁速度 (加快)
      color: options.color || 0x00b7ff, // 星星颜色 (天蓝色)
    };

    // Three.js 对象
    this.starGroups = [];
    this.geometries = [];
    this.materials = [];

    // 动画相关
    this.time = 0;
    this.rotationSpeeds = [];
    this.originalOpacities = [];
    this.originalSizes = [];
    this.twinkleOffsets = [];
    this.sizeOffsets = [];
    this.brightnessOffsets = [];

    this.init();
  }

  init() {
    this.createOrbitingStars();
    this.addToScene();
  }

  /**
   * 创建多层轨道的星星
   */
  createOrbitingStars() {
    const starsPerLayer = Math.floor(this.config.starCount / this.config.orbitLayers);

    for (let layer = 0; layer < this.config.orbitLayers; layer++) {
      this.createStarLayer(layer, starsPerLayer);
    }
  }

  /**
   * 创建单个轨道层的星星
   */
  createStarLayer(layerIndex, starCount) {
    // 计算当前层的轨道半径
    const radiusProgress = layerIndex / (this.config.orbitLayers - 1);
    const orbitRadius = this.config.minRadius + (this.config.maxRadius - this.config.minRadius) * radiusProgress;

    // 计算旋转速度（内层更快）
    const speedMultiplier = 1 + (this.config.orbitLayers - layerIndex - 1) * 0.5;
    this.rotationSpeeds[layerIndex] = this.config.rotationSpeed * speedMultiplier;

    // 创建实例化几何体用于星星
    const starGeometry = new THREE.SphereGeometry(0.02, 8, 6); // 小球体作为星星
    const instancedGeometry = new THREE.InstancedBufferGeometry();
    instancedGeometry.copy(starGeometry);
    instancedGeometry.instanceCount = starCount;

    // 创建实例化属性
    const instancePositions = new Float32Array(starCount * 3);
    const instanceSizes = new Float32Array(starCount);
    const instanceOpacities = new Float32Array(starCount);
    const layerOpacities = [];
    const layerSizes = [];
    const layerTwinkleOffsets = [];
    const layerSizeOffsets = [];
    const layerBrightnessOffsets = [];

    // 生成星星位置和属性
    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3;

      // 在球面上更均匀分布，减少聚集
      const theta = (i / starCount) * Math.PI * 2 + Math.random() * 0.2; // 方位角，减少随机性
      const phi = Math.acos(2 * Math.random() - 1); // 极角

      // 减少半径变化，让星星更贴近轨道
      const radiusVariation = orbitRadius + (Math.random() - 0.5) * EARTH_RADIUS * 0.05;

      // 转换为笛卡尔坐标
      instancePositions[i3] = radiusVariation * Math.sin(phi) * Math.cos(theta);
      instancePositions[i3 + 1] = radiusVariation * Math.sin(phi) * Math.sin(theta);
      instancePositions[i3 + 2] = radiusVariation * Math.cos(phi);

      // 随机大小（外层星星稍大）
      const sizeMultiplier = 0.8 + radiusProgress * 0.4;
      const size = (this.config.minSize + Math.random() * (this.config.maxSize - this.config.minSize)) * sizeMultiplier;
      instanceSizes[i] = size;

      // 随机透明度
      const opacity = this.config.minOpacity + Math.random() * (this.config.maxOpacity - this.config.minOpacity);
      instanceOpacities[i] = opacity;

      // 保存原始值和动画偏移
      layerOpacities[i] = opacity;
      layerSizes[i] = size;
      layerTwinkleOffsets[i] = Math.random() * Math.PI * 2;
      layerSizeOffsets[i] = Math.random() * Math.PI * 2;
      layerBrightnessOffsets[i] = Math.random() * Math.PI * 2;
    }

    this.originalOpacities[layerIndex] = layerOpacities;
    this.originalSizes[layerIndex] = layerSizes;
    this.twinkleOffsets[layerIndex] = layerTwinkleOffsets;
    this.sizeOffsets[layerIndex] = layerSizeOffsets;
    this.brightnessOffsets[layerIndex] = layerBrightnessOffsets;

    // 设置实例化属性
    instancedGeometry.setAttribute("instancePosition", new THREE.InstancedBufferAttribute(instancePositions, 3));
    instancedGeometry.setAttribute("size", new THREE.InstancedBufferAttribute(instanceSizes, 1));
    instancedGeometry.setAttribute("opacity", new THREE.InstancedBufferAttribute(instanceOpacities, 1));

    // 创建材质
    const material = this.createStarMaterial(layerIndex);

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(starGeometry, material, starCount);
    instancedMesh.name = `OrbitingStars_Layer_${layerIndex}`;

    // 设置每个实例的变换矩阵
    const matrix = new THREE.Matrix4();
    for (let i = 0; i < starCount; i++) {
      const position = new THREE.Vector3(instancePositions[i * 3], instancePositions[i * 3 + 1], instancePositions[i * 3 + 2]);
      const scale = instanceSizes[i];
      matrix.makeScale(scale, scale, scale);
      matrix.setPosition(position);
      instancedMesh.setMatrixAt(i, matrix);
    }
    instancedMesh.instanceMatrix.needsUpdate = true;

    // 创建旋转组
    const rotationGroup = new THREE.Group();
    rotationGroup.add(instancedMesh);

    this.starGroups.push(rotationGroup);
    this.geometries.push(instancedGeometry);
    this.materials.push(material);
    this.instancedMeshes = this.instancedMeshes || [];
    this.instancedMeshes.push(instancedMesh);

    // 清理临时几何体
    starGeometry.dispose();
  }

  /**
   * 创建星星材质
   */
  createStarMaterial(layerIndex) {
    // 创建星星纹理
    const canvas = document.createElement("canvas");
    canvas.width = 16;
    canvas.height = 16;
    const context = canvas.getContext("2d");

    // 绘制发光的星星
    const gradient = context.createRadialGradient(8, 8, 0, 8, 8, 8);
    gradient.addColorStop(0, "rgba(255, 255, 255, 1)");
    gradient.addColorStop(0.3, "rgba(0, 183, 255, 1)"); // 天蓝色
    gradient.addColorStop(0.6, "rgba(0, 183, 255, 0.4)");
    gradient.addColorStop(1, "rgba(135, 206, 235, 0)");

    context.fillStyle = gradient;
    context.fillRect(0, 0, 16, 16);

    const texture = new THREE.CanvasTexture(canvas);

    // 自定义着色器代码
    const vertexShader = `
      attribute vec3 instancePosition;
      attribute float size;
      attribute float opacity;
      varying float vOpacity;
      varying vec3 vPosition;
      varying vec3 vWorldPos;
      varying vec3 vNormal11;
      varying float vInstanceIndex;

      void main() {
        vOpacity = opacity;
        vInstanceIndex = float(gl_InstanceID);

        // 应用实例位置和缩放
        vec3 scaledPosition = position * size;
        vec3 finalPosition = scaledPosition + instancePosition;

        vPosition = finalPosition;
        vNormal11 = normalize(normalMatrix * normal);

        // 计算世界坐标
        vec4 worldPos = modelMatrix * vec4(finalPosition, 1.0);
        vWorldPos = worldPos.xyz;

        gl_Position = projectionMatrix * modelViewMatrix * vec4(finalPosition, 1.0);
      }
    `;

    const fragmentShader = `
      uniform sampler2D starMap;
      uniform vec3 starColor;
      uniform float uTime;
      uniform float uMetalness;
      uniform float uRoughness;
      uniform float uEmissiveIntensity;
      varying float vOpacity;
      varying vec3 vPosition;
      varying vec3 vWorldPos;
      varying vec3 vNormal11;
      varying float vInstanceIndex;

      void main() {
        // 使用UV坐标而不是gl_PointCoord（因为现在是网格而不是点）
        vec2 uv = vec2(0.5); // 中心点，或者可以使用实际UV
        vec4 texColor = texture2D(starMap, uv);

        // 多重颜色变化，使用实例索引增加变化
        float colorVariation1 = sin(vPosition.x * 0.01 + uTime + vInstanceIndex * 0.1) * 0.15 + 0.85;
        float colorVariation2 = sin(vPosition.y * 0.008 + uTime * 1.3 + vInstanceIndex * 0.05) * 0.1 + 0.9;
        float colorVariation3 = sin(vPosition.z * 0.012 + uTime * 0.7 + vInstanceIndex * 0.08) * 0.2 + 0.8;

        // 增强亮度脉冲
        float brightness = sin(uTime * 2.0 + vPosition.x * 0.1 + vInstanceIndex * 0.2) * 0.4 + 1.3;

        // 颜色温度变化
        vec3 warmColor = vec3(1.0, 0.9, 0.7); // 暖色调
        vec3 coolColor = vec3(0.7, 0.9, 1.0); // 冷色调
        float temperature = sin(uTime * 0.5 + vPosition.y * 0.05 + vInstanceIndex * 0.1) * 0.5 + 0.5;
        vec3 temperatureColor = mix(coolColor, warmColor, temperature);

        // 组合所有效果，增强整体亮度
        vec3 finalColor = starColor * colorVariation1 * colorVariation2 * colorVariation3 * brightness * 1.5;
        finalColor = mix(finalColor, finalColor * temperatureColor, 0.0);

        // 增强透明度以提高可见度
        float enhancedAlpha = vOpacity * 1.2;

        // 设置材质属性用于PBR渲染
        csm_DiffuseColor = vec4(finalColor, enhancedAlpha);
        csm_Emissive = finalColor * uEmissiveIntensity;
        csm_Metalness = uMetalness;
        csm_Roughness = uRoughness;
      }
    `;

    // 使用 CustomShaderMaterial 结合 MeshStandardMaterial
    return new CustomShaderMaterial({
      baseMaterial: THREE.MeshStandardMaterial,
      vertexShader,
      fragmentShader,
      uniforms: {
        starMap: { value: texture },
        starColor: { value: new THREE.Color(this.config.color) },
        uTime: { value: 0 },
        uMetalness: { value: 0.1 },
        uRoughness: { value: 0.8 },
        uEmissiveIntensity: { value: 2.0 },
      },
      // MeshStandardMaterial 属性
      map: texture,
      color: new THREE.Color(this.config.color),
      emissive: new THREE.Color(this.config.color),
      emissiveIntensity: 1.5,
      metalness: 0.1,
      roughness: 0.8,
      transparent: true,
      blending: THREE.AdditiveBlending,
      depthWrite: false,
    });
  }

  /**
   * 添加到场景
   */
  addToScene() {
    this.starGroups.forEach((group) => {
      if (this.scene) {
        this.scene.add(group);
      }
    });
  }

  /**
   * 更新动画
   */
  update() {
    this.time += 0.016; // 约60fps

    // 更新每个轨道层
    this.starGroups.forEach((group, layerIndex) => {
      // 旋转轨道
      group.rotation.y += this.rotationSpeeds[layerIndex];
      group.rotation.x += this.rotationSpeeds[layerIndex] * 0.3; // 添加一些倾斜旋转

      // 更新材质时间
      if (this.materials[layerIndex] && this.materials[layerIndex].uniforms) {
        this.materials[layerIndex].uniforms.uTime.value = this.time;
      }

      // 更新闪烁效果
      this.updateTwinkle(layerIndex);

      // 更新大小变化
      this.updateSizeAnimation(layerIndex);
    });
  }

  /**
   * 更新闪烁效果
   */
  updateTwinkle(layerIndex) {
    const geometry = this.geometries[layerIndex];
    const instancedMesh = this.instancedMeshes[layerIndex];
    if (!geometry || !geometry.attributes.opacity || !instancedMesh) return;

    const opacities = geometry.attributes.opacity.array;
    const originalOpacities = this.originalOpacities[layerIndex];
    const twinkleOffsets = this.twinkleOffsets[layerIndex];

    for (let i = 0; i < opacities.length; i++) {
      // 增强闪烁效果：使用多重正弦波叠加
      const primaryTwinkle = Math.sin(this.time * this.config.twinkleSpeed + twinkleOffsets[i]);
      const secondaryTwinkle = Math.sin(this.time * this.config.twinkleSpeed * 1.7 + twinkleOffsets[i] * 2);
      const tertiaryTwinkle = Math.sin(this.time * this.config.twinkleSpeed * 0.3 + twinkleOffsets[i] * 3);

      // 组合闪烁效果，创造更复杂的亮度变化
      const combinedTwinkle = (primaryTwinkle * 0.5 + secondaryTwinkle * 0.3 + tertiaryTwinkle * 0.2) * 0.5 + 0.5;

      // 添加脉冲效果
      const pulseEffect = Math.sin(this.time * this.config.twinkleSpeed * 0.1 + twinkleOffsets[i]) * 0.2 + 0.8;

      // 添加随机闪烁强度
      const intensity = 0.1 + Math.sin(twinkleOffsets[i] + this.time * 0.002) * 0.4;

      // 最终透明度计算 - 提高基础亮度
      const finalOpacity = originalOpacities[i] * combinedTwinkle * pulseEffect * (0.6 + intensity * 0.4);
      opacities[i] = Math.max(0.3, Math.min(1.0, finalOpacity)); // 提高最小透明度确保更亮
    }

    geometry.attributes.opacity.needsUpdate = true;
  }

  /**
   * 更新大小动画效果
   */
  updateSizeAnimation(layerIndex) {
    const geometry = this.geometries[layerIndex];
    const instancedMesh = this.instancedMeshes[layerIndex];
    if (!geometry || !geometry.attributes.size || !instancedMesh) return;

    const sizes = geometry.attributes.size.array;
    const originalSizes = this.originalSizes[layerIndex];
    const sizeOffsets = this.sizeOffsets[layerIndex];
    const brightnessOffsets = this.brightnessOffsets[layerIndex];
    const instancePositions = geometry.attributes.instancePosition.array;

    // 更新实例矩阵以反映大小变化
    const matrix = new THREE.Matrix4();

    for (let i = 0; i < sizes.length; i++) {
      // 大小变化动画 - 增加速度和变化幅度
      const sizeWave = Math.sin(this.time * this.config.twinkleSpeed * 1.5 + sizeOffsets[i]);
      const sizeMultiplier = 0.6 + sizeWave * 0.5; // 大小在60%到110%之间变化，增加变化幅度

      // 亮度相关的大小变化 - 增加速度和变化幅度
      const brightnessWave = Math.sin(this.time * this.config.twinkleSpeed * 2.0 + brightnessOffsets[i]);
      const brightnessMultiplier = 0.7 + brightnessWave * 0.4; // 增加变化幅度

      // 组合效果
      const finalSize = originalSizes[i] * sizeMultiplier * brightnessMultiplier;
      sizes[i] = finalSize;

      // 更新实例矩阵
      const position = new THREE.Vector3(instancePositions[i * 3], instancePositions[i * 3 + 1], instancePositions[i * 3 + 2]);
      matrix.makeScale(finalSize, finalSize, finalSize);
      matrix.setPosition(position);
      instancedMesh.setMatrixAt(i, matrix);
    }

    geometry.attributes.size.needsUpdate = true;
    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  /**
   * 设置可见性
   */
  setVisible(visible) {
    this.starGroups.forEach((group) => {
      group.visible = visible;
    });
  }

  /**
   * 设置颜色
   */
  setColor(color) {
    this.config.color = color;
    this.materials.forEach((material) => {
      if (material.uniforms && material.uniforms.color) {
        material.uniforms.color.value.set(color);
      }
    });
  }

  /**
   * 销毁资源
   */
  destroy() {
    // 从场景中移除
    this.starGroups.forEach((group) => {
      if (this.scene) {
        this.scene.remove(group);
      }
    });

    // 清理几何体
    this.geometries.forEach((geometry) => {
      geometry.dispose();
    });

    // 清理材质
    this.materials.forEach((material) => {
      if (material.uniforms && material.uniforms.map && material.uniforms.map.value) {
        material.uniforms.map.value.dispose();
      }
      material.dispose();
    });

    // 清理实例化网格
    if (this.instancedMeshes) {
      this.instancedMeshes.forEach((mesh) => {
        if (mesh.geometry) mesh.geometry.dispose();
        if (mesh.material) mesh.material.dispose();
      });
    }

    // 清理数组
    this.starGroups = [];
    this.geometries = [];
    this.materials = [];
    this.instancedMeshes = [];
    this.rotationSpeeds = [];
    this.originalOpacities = [];
    this.originalSizes = [];
    this.twinkleOffsets = [];
    this.sizeOffsets = [];
    this.brightnessOffsets = [];
  }
}

export { OrbitingStars };
