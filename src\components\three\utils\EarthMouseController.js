import * as THREE from "three";

/**
 * 地球原生鼠标控制器
 * 替代OrbitControls，使用原生鼠标事件实现地球旋转功能
 */
class EarthMouseController {
  constructor(camera, renderer, earthGroup, elementsGroup = null) {
    console.log("EarthMouseController constructor called");

    try {
      this.camera = camera;
      this.renderer = renderer;
      this.earthGroup = earthGroup;
      this.elementsGroup = elementsGroup || earthGroup; // 如果没有提供elementsGroup，则使用earthGroup
      this.domElement = renderer.domElement;

      // 目标位置（兼容OrbitControls接口）
      this.target = new THREE.Vector3(0, -1, -2.7);
      console.log("Target initialized:", this.target);

      // 默认状态保存（用于恢复）
      this.defaultState = {
        elementsRotation: new THREE.Euler(-0.0009438373711884839, -3.6725769743962937, 0),
        cameraPosition: new THREE.Vector3(6.607814630433295, 23.527156235789565, 33.78524722016197),
        target: new THREE.Vector3(0, -1, -2.7),
        defaultDistance: 40, // 默认缩放距离（在min和max之间的中间值）
      };

      // 鼠标状态
      this.isMouseDown = false;
      this.mouseX = 0;
      this.mouseY = 0;
      this.lastMouseX = 0;
      this.lastMouseY = 0;

      // 旋转参数
      this.rotationSpeed = 0.005; // 旋转速度
      // this.dampingFactor = 0.95; // 阻尼系数
      this.dampingFactor = 0.0; // 阻尼系数
      this.autoRotateSpeed = 0.001; // 自动旋转速度
      this.enableAutoRotate = false; // 是否启用自动旋转

      // 垂直旋转限制参数
      this.enableVerticalLimit = true; // 是否启用垂直旋转限制
      this.maxVerticalAngle = Math.PI / 3; // 最大垂直旋转角度（60度）
      this.minVerticalAngle = -Math.PI / 3; // 最小垂直旋转角度（-60度）

      // 当前旋转速度（用于惯性）
      this.currentRotationX = 0;
      this.currentRotationY = 0;

      // 缩放参数
      this.enableZoom = true; // 是否启用缩放
      this.zoomSpeed = 0.1; // 缩放速度
      this.minDistance = 30; // 最小距离
      this.maxDistance = 50; // 最大距离
      this.currentZoomVelocity = 0; // 当前缩放速度（用于惯性）
      this.zoomDampingFactor = 0.9; // 缩放阻尼系数

      // 空闲自动旋转参数
      this.enableIdleAutoRotate = false; // 是否启用空闲自动旋转
      this.idleTimeout = 3000; // 空闲时间阈值（毫秒）
      this.lastInteractionTime = Date.now(); // 最后交互时间
      this.isIdle = false; // 是否处于空闲状态
      this.idleAutoRotateSpeed = 0.002; // 空闲自动旋转速度
      this.isResettingToDefault = false; // 是否正在恢复到默认状态

      // 调整相机位置，使其看向目标点（模拟OrbitControls的行为）
      this.adjustCameraToTarget();

      // 事件监听器
      this.eventListeners = {};

      // 绑定事件
      this.bindEvents();

      console.log("EarthMouseController initialized successfully");
      console.log("Zoom enabled:", this.enableZoom, "Min distance:", this.minDistance, "Max distance:", this.maxDistance);
      console.log("Idle auto-rotate enabled:", this.enableIdleAutoRotate, "Idle timeout:", this.idleTimeout + "ms");
    } catch (error) {
      console.error("Error in EarthMouseController constructor:", error);
      throw error;
    }
  }

  /**
   * 调整相机位置，使其看向目标点（模拟OrbitControls的行为）
   */
  adjustCameraToTarget() {
    // 让相机看向目标点
    this.camera.lookAt(this.target);
    console.log("Camera adjusted to look at target:", this.target);
    console.log("Camera position:", this.camera.position);
  }

  /**
   * 更新交互时间并重置空闲状态
   */
  updateInteractionTime() {
    this.lastInteractionTime = Date.now();
    if (this.isIdle) {
      this.isIdle = false;
      console.log("User interaction detected, stopping idle auto-rotate");
    }
  }

  /**
   * 绑定鼠标事件
   */
  bindEvents() {
    this.onMouseDown = this.onMouseDown.bind(this);
    this.onMouseMove = this.onMouseMove.bind(this);
    this.onMouseUp = this.onMouseUp.bind(this);
    this.onContextMenu = this.onContextMenu.bind(this);
    this.onWheel = this.onWheel.bind(this);

    this.domElement.addEventListener("mousedown", this.onMouseDown);
    this.domElement.addEventListener("mousemove", this.onMouseMove);
    this.domElement.addEventListener("mouseup", this.onMouseUp);
    this.domElement.addEventListener("contextmenu", this.onContextMenu);
    this.domElement.addEventListener("wheel", this.onWheel, { passive: false });

    // 防止选择文本
    this.domElement.style.userSelect = "none";
    this.domElement.style.webkitUserSelect = "none";
  }

  /**
   * 鼠标按下事件
   */
  onMouseDown(event) {
    event.preventDefault();

    this.isMouseDown = true;
    this.lastMouseX = event.clientX;
    this.lastMouseY = event.clientY;

    // 重置当前旋转速度
    this.currentRotationX = 0;
    this.currentRotationY = 0;

    // 记录交互时间
    this.updateInteractionTime();

    // 触发用户交互事件（用于隐藏弹窗和恢复自动旋转）
    this.dispatchEvent("userInteractionStart", {
      type: "mousedown",
      event: event,
    });

    this.domElement.style.cursor = "grabbing";
  }

  /**
   * 鼠标移动事件
   */
  onMouseMove(event) {
    event.preventDefault();

    if (!this.isMouseDown) return;

    const deltaX = event.clientX - this.lastMouseX;
    const deltaY = event.clientY - this.lastMouseY;

    // 计算旋转增量
    this.currentRotationX = deltaY * this.rotationSpeed; // 恢复正常垂直方向
    this.currentRotationY = deltaX * this.rotationSpeed;

    // 应用垂直旋转限制
    if (this.enableVerticalLimit) {
      const newRotationX = this.elementsGroup.rotation.x + this.currentRotationX;

      // 检查是否超出垂直旋转限制
      if (newRotationX > this.maxVerticalAngle) {
        this.currentRotationX = this.maxVerticalAngle - this.elementsGroup.rotation.x;
      } else if (newRotationX < this.minVerticalAngle) {
        this.currentRotationX = this.minVerticalAngle - this.elementsGroup.rotation.x;
      }
    }

    // 直接旋转元素组（而不是整个地球组）
    this.elementsGroup.rotation.x += this.currentRotationX;
    this.elementsGroup.rotation.y += this.currentRotationY;

    // 记录交互时间
    this.updateInteractionTime();

    this.lastMouseX = event.clientX;
    this.lastMouseY = event.clientY;
  }

  /**
   * 鼠标抬起事件
   */
  onMouseUp(event) {
    event.preventDefault();

    this.isMouseDown = false;
    this.domElement.style.cursor = "grab";

    // 记录交互时间
    this.updateInteractionTime();
  }

  /**
   * 禁用右键菜单
   */
  onContextMenu(event) {
    event.preventDefault();
  }

  /**
   * 鼠标滚轮事件（缩放）
   */
  onWheel(event) {
    if (!this.enableZoom) return;

    event.preventDefault();

    // 获取滚轮方向（标准化处理）
    const delta = event.deltaY > 0 ? 1 : -1;

    // 计算缩放增量
    const zoomDelta = delta * this.zoomSpeed;
    this.currentZoomVelocity += zoomDelta;

    // 记录交互时间
    this.updateInteractionTime();

    // 立即应用缩放
    this.applyZoom(zoomDelta);
  }

  /**
   * 应用缩放变换
   */
  applyZoom(zoomDelta) {
    // 计算相机到目标点的方向向量
    const direction = new THREE.Vector3();
    direction.subVectors(this.camera.position, this.target).normalize();

    // 计算当前距离
    const currentDistance = this.camera.position.distanceTo(this.target);

    // 计算新的距离
    const newDistance = Math.max(this.minDistance, Math.min(this.maxDistance, currentDistance + zoomDelta));

    // 如果距离没有变化（达到边界），则不进行缩放
    if (Math.abs(newDistance - currentDistance) < 0.001) {
      this.currentZoomVelocity *= 0.5; // 减少速度以避免在边界处震荡
      return;
    }

    // 计算新的相机位置
    const newPosition = new THREE.Vector3();
    newPosition.copy(this.target).add(direction.multiplyScalar(newDistance));

    // 更新相机位置
    this.camera.position.copy(newPosition);

    // 确保相机仍然看向目标点
    this.camera.lookAt(this.target);
  }

  /**
   * 更新控制器（在动画循环中调用）
   */
  update() {
    // 检查空闲状态
    this.checkIdleState();

    // 如果没有鼠标交互，应用惯性和自动旋转
    if (!this.isMouseDown) {
      // 应用旋转惯性
      if (Math.abs(this.currentRotationX) > 0.001 || Math.abs(this.currentRotationY) > 0.001) {
        // 应用垂直旋转限制（惯性旋转时）
        if (this.enableVerticalLimit && Math.abs(this.currentRotationX) > 0.001) {
          const newRotationX = this.elementsGroup.rotation.x + this.currentRotationX;

          // 检查是否超出垂直旋转限制
          if (newRotationX > this.maxVerticalAngle) {
            this.currentRotationX = this.maxVerticalAngle - this.elementsGroup.rotation.x;
            // 当达到限制时，停止垂直惯性
            if (Math.abs(this.currentRotationX) < 0.001) {
              this.currentRotationX = 0;
            }
          } else if (newRotationX < this.minVerticalAngle) {
            this.currentRotationX = this.minVerticalAngle - this.elementsGroup.rotation.x;
            // 当达到限制时，停止垂直惯性
            if (Math.abs(this.currentRotationX) < 0.001) {
              this.currentRotationX = 0;
            }
          }
        }

        // 继续旋转元素组（而不是整个地球组）
        this.elementsGroup.rotation.x += this.currentRotationX;
        this.elementsGroup.rotation.y += this.currentRotationY;

        // 应用阻尼
        this.currentRotationX *= this.dampingFactor;
        this.currentRotationY *= this.dampingFactor;
      }

      // 应用缩放惯性
      if (this.enableZoom && Math.abs(this.currentZoomVelocity) > 0.001) {
        this.applyZoom(this.currentZoomVelocity);
        this.currentZoomVelocity *= this.zoomDampingFactor;
      }

      // 手动启用的自动旋转
      if (this.enableAutoRotate) {
        this.elementsGroup.rotation.y += this.autoRotateSpeed;
      }

      // 空闲自动旋转（优先级较低）
      if (this.enableIdleAutoRotate && this.isIdle && !this.enableAutoRotate && !this.isResettingToDefault) {
        this.elementsGroup.rotation.y += this.idleAutoRotateSpeed;
      }
    }
  }

  /**
   * 检查空闲状态
   */
  checkIdleState() {
    const currentTime = Date.now();
    const timeSinceLastInteraction = currentTime - this.lastInteractionTime;

    if (!this.isIdle && timeSinceLastInteraction > this.idleTimeout) {
      this.isIdle = true;
      console.log("User idle detected, resetting to default state before starting auto-rotate");

      // 在开启自动旋转之前，先恢复到默认状态
      this.resetToDefaultState();
    }
  }

  /**
   * 设置目标位置（兼容OrbitControls接口）
   */
  setTarget(x, y, z) {
    this.target.set(x, y, z);
    // 当目标改变时，重新调整相机朝向
    this.adjustCameraToTarget();
  }

  /**
   * 启用/禁用自动旋转
   */
  setAutoRotate(enabled) {
    this.enableAutoRotate = enabled;
  }

  /**
   * 启用/禁用空闲自动旋转
   */
  setIdleAutoRotate(enabled) {
    this.enableIdleAutoRotate = enabled;
    console.log("Idle auto-rotate enabled:", this.enableIdleAutoRotate);
  }

  /**
   * 暂停自动旋转（保存当前状态）
   */
  pauseAutoRotation() {
    this.autoRotationPaused = true;
    this.savedAutoRotateState = this.enableAutoRotate;
    this.savedIdleAutoRotateState = this.enableIdleAutoRotate;
    this.enableAutoRotate = false;
    this.enableIdleAutoRotate = false;
    console.log("Auto rotation paused");
  }

  /**
   * 恢复自动旋转（恢复之前的状态）
   */
  resumeAutoRotation() {
    if (this.autoRotationPaused) {
      this.enableAutoRotate = this.savedAutoRotateState || false;
      this.enableIdleAutoRotate = this.savedIdleAutoRotateState !== undefined ? this.savedIdleAutoRotateState : true;
      this.autoRotationPaused = false;
      console.log("Auto rotation resumed");
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType, callback) {
    if (!this.eventListeners[eventType]) {
      this.eventListeners[eventType] = [];
    }
    this.eventListeners[eventType].push(callback);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType, callback) {
    if (this.eventListeners[eventType]) {
      const index = this.eventListeners[eventType].indexOf(callback);
      if (index > -1) {
        this.eventListeners[eventType].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  dispatchEvent(eventType, data = {}) {
    if (this.eventListeners[eventType]) {
      this.eventListeners[eventType].forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * 设置空闲时间阈值
   */
  setIdleTimeout(timeout) {
    this.idleTimeout = Math.max(1000, timeout); // 最小1秒
    console.log("Idle timeout set to:", this.idleTimeout + "ms");
  }

  /**
   * 设置空闲自动旋转速度
   */
  setIdleAutoRotateSpeed(speed) {
    this.idleAutoRotateSpeed = Math.max(0.0001, Math.min(0.01, speed)); // 限制在合理范围内
    console.log("Idle auto-rotate speed set to:", this.idleAutoRotateSpeed);
  }

  /**
   * 手动重置空闲状态
   */
  resetIdleState() {
    this.updateInteractionTime();
    console.log("Idle state manually reset");
  }

  /**
   * 获取当前是否处于空闲状态
   */
  getIsIdle() {
    return this.isIdle;
  }

  /**
   * 手动触发恢复到默认状态
   * @param {number} duration - 动画持续时间（毫秒）
   */
  triggerResetToDefault(duration = 2000) {
    console.log("手动触发恢复到默认状态");
    this.resetToDefaultState(duration);
  }

  /**
   * 更新默认状态（允许动态设置新的默认状态）
   * @param {Object} newDefaultState - 新的默认状态
   */
  updateDefaultState(newDefaultState) {
    if (newDefaultState.elementsRotation) {
      this.defaultState.elementsRotation.copy(newDefaultState.elementsRotation);
    }
    if (newDefaultState.cameraPosition) {
      this.defaultState.cameraPosition.copy(newDefaultState.cameraPosition);
    }
    if (newDefaultState.target) {
      this.defaultState.target.copy(newDefaultState.target);
    }
    if (newDefaultState.defaultDistance !== undefined) {
      this.defaultState.defaultDistance = newDefaultState.defaultDistance;
    }
    console.log("默认状态已更新:", this.defaultState);
  }

  /**
   * 获取当前默认状态
   * @returns {Object} 默认状态对象的副本
   */
  getDefaultState() {
    return {
      elementsRotation: this.defaultState.elementsRotation.clone(),
      cameraPosition: this.defaultState.cameraPosition.clone(),
      target: this.defaultState.target.clone(),
      defaultDistance: this.defaultState.defaultDistance,
    };
  }

  /**
   * 设置旋转速度
   */
  setRotationSpeed(speed) {
    this.rotationSpeed = speed;
  }

  /**
   * 设置阻尼系数
   */
  setDampingFactor(factor) {
    this.dampingFactor = factor;
  }

  /**
   * 启用/禁用缩放功能
   */
  setEnableZoom(enabled) {
    this.enableZoom = enabled;
    console.log("Zoom enabled:", this.enableZoom);
  }

  /**
   * 设置缩放速度
   */
  setZoomSpeed(speed) {
    this.zoomSpeed = Math.max(0.01, Math.min(1.0, speed)); // 限制在合理范围内
    console.log("Zoom speed set to:", this.zoomSpeed);
  }

  /**
   * 设置缩放距离限制
   */
  setZoomLimits(minDistance, maxDistance) {
    this.minDistance = Math.max(1, minDistance); // 最小距离不能小于1
    this.maxDistance = Math.max(this.minDistance + 1, maxDistance); // 最大距离必须大于最小距离
    console.log("Zoom limits set - Min:", this.minDistance, "Max:", this.maxDistance);
  }

  /**
   * 设置缩放阻尼系数
   */
  setZoomDampingFactor(factor) {
    this.zoomDampingFactor = Math.max(0.1, Math.min(0.99, factor)); // 限制在合理范围内
    console.log("Zoom damping factor set to:", this.zoomDampingFactor);
  }

  /**
   * 启用/禁用垂直旋转限制
   */
  setEnableVerticalLimit(enabled) {
    this.enableVerticalLimit = enabled;
    console.log("Vertical rotation limit enabled:", this.enableVerticalLimit);
  }

  /**
   * 设置垂直旋转角度限制
   * @param {number} maxAngle - 最大垂直角度（弧度），正值表示向上旋转
   * @param {number} minAngle - 最小垂直角度（弧度），负值表示向下旋转
   */
  setVerticalAngleLimits(maxAngle, minAngle) {
    // 确保角度在合理范围内（-π/2 到 π/2）
    this.maxVerticalAngle = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, maxAngle));
    this.minVerticalAngle = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, minAngle));

    // 确保最小角度小于最大角度
    if (this.minVerticalAngle > this.maxVerticalAngle) {
      const temp = this.minVerticalAngle;
      this.minVerticalAngle = this.maxVerticalAngle;
      this.maxVerticalAngle = temp;
    }

    console.log("Vertical angle limits set - Max:", ((this.maxVerticalAngle * 180) / Math.PI).toFixed(1) + "°", "Min:", ((this.minVerticalAngle * 180) / Math.PI).toFixed(1) + "°");
  }

  /**
   * 设置垂直旋转角度限制（使用度数）
   * @param {number} maxDegrees - 最大垂直角度（度数）
   * @param {number} minDegrees - 最小垂直角度（度数）
   */
  setVerticalAngleLimitsDegrees(maxDegrees, minDegrees) {
    const maxRadians = (maxDegrees * Math.PI) / 180;
    const minRadians = (minDegrees * Math.PI) / 180;
    this.setVerticalAngleLimits(maxRadians, minRadians);
  }

  /**
   * 获取当前垂直旋转角度限制（度数）
   */
  getVerticalAngleLimits() {
    return {
      maxDegrees: (this.maxVerticalAngle * 180) / Math.PI,
      minDegrees: (this.minVerticalAngle * 180) / Math.PI,
      maxRadians: this.maxVerticalAngle,
      minRadians: this.minVerticalAngle,
      enabled: this.enableVerticalLimit,
    };
  }

  /**
   * 获取当前相机距离目标点的距离
   */
  getCurrentDistance() {
    return this.camera.position.distanceTo(this.target);
  }

  /**
   * 恢复到默认状态（旋转和缩放）
   * @param {number} duration - 动画持续时间（毫秒）
   */
  resetToDefaultState(duration = 2000) {
    if (this.isResettingToDefault) {
      return; // 如果已经在恢复过程中，避免重复执行
    }

    this.isResettingToDefault = true;
    console.log("开始恢复到默认状态...");

    // 获取当前状态
    const currentRotation = {
      x: this.elementsGroup.rotation.x,
      y: this.elementsGroup.rotation.y,
      z: this.elementsGroup.rotation.z,
    };

    const currentDistance = this.getCurrentDistance();

    // 目标状态
    const targetRotation = {
      x: this.defaultState.elementsRotation.x,
      y: this.defaultState.elementsRotation.y,
      z: this.defaultState.elementsRotation.z,
    };

    const targetDistance = this.defaultState.defaultDistance;

    // 动画开始时间
    const startTime = Date.now();

    // 缓动函数（easeInOutCubic）
    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easeInOutCubic(progress);

      // 插值旋转
      this.elementsGroup.rotation.x = currentRotation.x + (targetRotation.x - currentRotation.x) * easedProgress;
      this.elementsGroup.rotation.y = currentRotation.y + (targetRotation.y - currentRotation.y) * easedProgress;
      this.elementsGroup.rotation.z = currentRotation.z + (targetRotation.z - currentRotation.z) * easedProgress;

      // 插值缩放（相机距离）
      const newDistance = currentDistance + (targetDistance - currentDistance) * easedProgress;
      const direction = new THREE.Vector3();
      direction.subVectors(this.camera.position, this.target).normalize();

      const newPosition = new THREE.Vector3();
      newPosition.copy(this.target).add(direction.multiplyScalar(newDistance));

      this.camera.position.copy(newPosition);
      this.camera.lookAt(this.target);

      // 清除惯性
      this.currentRotationX = 0;
      this.currentRotationY = 0;
      this.currentZoomVelocity = 0;

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // 动画完成
        this.isResettingToDefault = false;
        console.log("恢复到默认状态完成，开始自动旋转");

        // 确保精确设置到目标值
        this.elementsGroup.rotation.copy(this.defaultState.elementsRotation);

        // 触发恢复完成事件
        this.dispatchEvent("resetToDefaultComplete", {
          rotation: targetRotation,
          distance: targetDistance,
        });
      }
    };

    animate();
  }

  /**
   * 平滑缩放到指定距离
   */
  zoomToDistance(targetDistance, duration = 1000) {
    const currentDistance = this.getCurrentDistance();
    const distance = Math.max(this.minDistance, Math.min(this.maxDistance, targetDistance));

    if (Math.abs(distance - currentDistance) < 0.001) {
      return; // 距离差异太小，不需要动画
    }

    // 使用简单的线性插值实现平滑缩放
    const startTime = Date.now();
    const startDistance = currentDistance;
    const deltaDistance = distance - startDistance;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用easeInOut缓动函数
      const easeProgress = progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      const currentTargetDistance = startDistance + deltaDistance * easeProgress;
      const direction = new THREE.Vector3();
      direction.subVectors(this.camera.position, this.target).normalize();

      const newPosition = new THREE.Vector3();
      newPosition.copy(this.target).add(direction.multiplyScalar(currentTargetDistance));

      this.camera.position.copy(newPosition);
      this.camera.lookAt(this.target);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  /**
   * 销毁控制器
   */
  dispose() {
    this.domElement.removeEventListener("mousedown", this.onMouseDown);
    this.domElement.removeEventListener("mousemove", this.onMouseMove);
    this.domElement.removeEventListener("mouseup", this.onMouseUp);
    this.domElement.removeEventListener("contextmenu", this.onContextMenu);
    this.domElement.removeEventListener("wheel", this.onWheel);

    // 恢复默认样式
    this.domElement.style.cursor = "default";
    this.domElement.style.userSelect = "auto";
    this.domElement.style.webkitUserSelect = "auto";

    console.log("EarthMouseController disposed");
  }
}

export { EarthMouseController };
export default EarthMouseController;
